# Form Builder Application Documentation

## Overview

The Form Builder is a comprehensive web application designed for creating dynamic forms for various activities within the Babban Gona organization. It provides an intuitive drag-and-drop interface that allows users to build complex forms with multiple field types, validation rules, and custom properties.

### Key Capabilities

- **Visual Form Builder**: Drag-and-drop interface for creating forms
- **Multi-Screen Forms**: Support for forms with multiple screens/pages
- **Rich Field Types**: 14+ different input field types including text, number, date, file upload, etc.
- **Real-time Validation**: Form validation with custom rules and regex patterns
- **Auto-save**: Automatic saving of form progress

## Architecture

The application follows a modern React architecture with the following key patterns:

### Frontend Architecture

- **Next.js 15**: React framework with App Router
- **Component-Based**: Modular component architecture
- **State Management**: Redux Toolkit with Redux Persist
- **Data Fetching**: TanStack Query (React Query) for server state
- **Drag & Drop**: @dnd-kit for form building interactions
- **Styling**: Tailwind CSS with custom design system

### Backend Integration

- **RESTful API**: Integration with Form Builder API
- **Authentication**: JWT-based authentication with token refresh
- **File Upload**: Support for audio and media file uploads
- **Multi-tenant**: Organization-based data isolation

## Features

### Core Features

1. **Form Creation**
   - Create forms from scratch or duplicate existing forms
   - Multi-screen form support
   - Section-based organization

2. **Form Builder Interface**
   - Drag-and-drop form elements
   - Real-time preview
   - Properties panel for field configuration
   - Visual feedback for selected elements

3. **Field Types**
   - Short Answer Field
   - Paragraph Field
   - Number Field
   - Phone Number Field
   - Date Field
   - Time Field
   - Single Choice Field
   - Multiple Choice Field
   - File Upload Field
   - Camera Field
   - Audio Recording Field
   - Rating Field
   - Dropdown Field
   - Read-Only Field

4. **Validation & Logic**
   - Required field validation
   - Character count limits
   - Number range validation
   - Date range validation
   - Custom regex validation
   - Conditional logic support

5. **User Experience**
   - Auto-save functionality
   - Offline status detection
   - Loading states and skeletons
   - Error handling and recovery
   - Success notifications

## Technology Stack

### Development Tools

- **Package Manager**: pnpm
- **Linting**: ESLint with Next.js config
- **Formatting**: Prettier with Tailwind plugin
- **Git Hooks**: Husky for pre-commit hooks
- **Testing**: Jest configuration included

## Project Structure

``` bash
src/
├── api/                    # API layer and HTTP clients
│   ├── base.ts            # Axios instances and interceptors
│   └── forms.ts           # Form-related API calls
├── app/                   # Next.js App Router pages
│   ├── (core)/           # Protected routes group
│   │   └── forms/        # Form-related pages
│   ├── layout.tsx        # Root layout
│   └── page.tsx          # Home page (redirects to /forms)
├── assets/               # Static assets
│   ├── fonts/           # Custom fonts
│   ├── icons/           # SVG icons
│   └── images/          # Images
├── components/           # React components
│   ├── basics/          # Form field components
│   ├── dialogs/         # Modal dialogs
│   ├── layouts/         # Layout components
│   ├── skeletons/       # Loading skeletons
│   ├── ui/              # Base UI components (shadcn/ui)
│   └── *.tsx            # Core components
├── config/              # Configuration files
│   ├── env.ts           # Environment variables
│   └── tansack-query.ts # React Query config
├── hooks/               # Custom React hooks
│   ├── tansack-query/   # Data fetching hooks
│   └── *.ts             # Utility hooks
├── lib/                 # Utility libraries
│   ├── redux/           # Redux store and slices
│   ├── utils/           # Utility functions
│   └── constants.ts     # Application constants
├── providers/           # Redux providers
├── schemas/             # Zod validation schemas
└── types/               # TypeScript type definitions
```

## Installation & Setup

### Prerequisites

- Node.js 18+
- pnpm package manager
- Git

### Installation Steps

1. **Clone the repository**
  
   ```bash
   git clone https://github.com/BabbanGonaDev/agricos25-form-builder-app.git
   cd agricos25-form-builder-app
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env
   ```

   Edit `.env` with the appropriate configuration values.

4. **Start development server**

   ```bash
   pnpm dev
   ```

5. **Build for production**

   ```bash
   pnpm build
   pnpm start
   ```

### Available Scripts

- `pnpm dev` - Start development server with Turbopack
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint
- `pnpm format` - Format code with Prettier

## Environment Configuration

### Configuration Details

- **FORM_BUILDER_API**: Backend API endpoint for form operations
- **CONFIG_FRONTEND_URL**: Configuration service frontend URL
- **IAM_API**: Identity and Access Management API endpoint
- **IAM_FRONTEND_URL**: IAM frontend URL for authentication flows

## API Integration

### API Clients

The application uses two main API clients:

1. **Form Builder API** (`formBuilderApi`)
   - Handles form CRUD operations
   - File upload functionality
   - Form submission and retrieval

2. **IAM API** (`iamApi`)
   - Token refresh operations
   - Authentication management

### Key API Endpoints

- `POST /form` - Create new form
- `GET /form/:id` - Get single form
- `PUT /form/:id` - Update form
- `POST /form/:id/submit` - Submit form
- `GET /form/submitted` - Get submitted forms
- `POST /upload/single` - Upload file

## Form Elements

### Available Field Types

The form builder supports 14 different field types, each with specific properties and validation options:

#### Text Fields

- **Short Answer Field**: Single-line text input
- **Paragraph Field**: Multi-line text area
- **Number Field**: Numeric input with currency support
- **Phone Number Field**: Phone input with country code

#### Date/Time Fields

- **Date Field**: Date picker with range validation
- **Time Field**: Time picker with format options

#### Choice Fields

- **Single Choice Field**: Radio buttons or dropdown
- **Multiple Choice Field**: Checkboxes with multiple selection
- **Dropdown Field**: Select dropdown with options

#### Media Fields

- **File Field**: File upload with format restrictions
- **Camera Field**: Camera capture functionality
- **Audio Recording Field**: Audio recording capability

#### Special Fields

- **Rating Field**: Star or numeric rating system
- **Read-Only Field**: Display-only content with media support

### Field Properties

Each field type supports various properties:

```typescript
type FormElement = {
  // Basic Properties
  label: string;
  placeholder?: string;
  hint?: string;
  tooltip?: string;
  required: boolean;
  tag?: string;
  validate?: boolean;

  // Type-specific properties
  minimumCharacterCount?: number;
  maximumCharacterCount?: number;
  minimumValue?: number;
  maximumValue?: number;
  options?: string[];
  // ... many more
}
```

## State Management

### Redux Store Structure

The application uses Redux Toolkit with three main slices:

#### 1. Auth Slice (`authSlice`)

```typescript
type AuthState = {
  userTokens: {
    accessToken: string;
    refreshToken: string;
  };
  isSomethingWrong: boolean;
}
```

#### 2. Form Slice (`formSlice`)

```typescript
type FormState = {
  formScreens: FormScreen[];
  formId: string;
  selectedFormBuilderItem: SelectedItem | null;
  selectedFormBuilderItemScreen: string;
}
```

#### 3. Dialog Slice (`dialogSlice`)

```typescript
type DialogState = {
  isConfirmationDialogOpen: boolean;
  isCreateFormDialogOpen: boolean;
  isSuccessDialogOpen: boolean;
  successMessage: string;
  confirmationMessage: string;
}
```

### Data Flow

1. **Form Building**: User interactions update the `formSlice` state
2. **Auto-save**: Changes trigger API calls via TanStack Query
3. **Persistence**: Auth tokens are persisted using Redux Persist
4. **Real-time Updates**: UI reflects state changes immediately

## Authentication

### Authentication Flow

1. **Token-based Authentication**: JWT access and refresh tokens
2. **Automatic Refresh**: Interceptors handle token refresh on 401 errors
3. **URL Parameters**: Tokens can be passed via URL parameters
4. **Session Storage**: Return URLs stored for navigation

### AuthWrapper Component

The `AuthWrapper` HOC handles:

- Token extraction from URL parameters
- Token storage in Redux store
- Return URL management
- Error state handling

```typescript
export default function AuthWrapper<P extends object>(Component: ComponentType<P>) {
  return function IsAuth(props: P) {
    // Token management logic
    // Error handling
    // Navigation logic
  };
}
```

## Development Guidelines

### Code Style

- **TypeScript**: Strict type checking enabled
- **ESLint**: Next.js recommended configuration
- **Prettier**: Consistent code formatting
- **Tailwind**: Utility-first CSS approach

### Component Guidelines

1. **Functional Components**: Use function declarations
2. **Custom Hooks**: Extract reusable logic
3. **Type Safety**: Comprehensive TypeScript usage
4. **Error Boundaries**: Implement error handling
5. **Accessibility**: Follow WCAG guidelines

### State Management Best Practices

1. **Minimal State**: Keep only necessary data in Redux
2. **Normalized Data**: Use normalized state structure
3. **Async Logic**: Use TanStack Query for server state

### Performance Considerations

1. **Code Splitting**: Lazy load components where appropriate
2. **Memoization**: Use React.memo and useMemo strategically
3. **Bundle Analysis**: Monitor bundle size
4. **Image Optimization**: Use Next.js Image component
