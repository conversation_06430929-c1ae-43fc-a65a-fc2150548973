import FormElement from "@/components/FormElement";
import MenuBarElement from "@/components/MenuBarElement";
import type { ElementType } from "@/components/types";
import { FIELD_COMPONENTS } from "@/lib/constants";
import { DragOverlay, useDndMonitor, type Active, type DragStartEvent } from "@dnd-kit/core";
import { type StaticImageData } from "next/image";
import { ReactNode, useState } from "react";
import SectionLayout from "./layouts/Section/SectionLayout";

const DragOverlayWrapper = () => {
  const [draggedItem, setDraggedItem] = useState<Active | null>(null);
  useDndMonitor({
    onDragStart: (event: DragStartEvent) => {
      setDraggedItem(event.active);
    },
    onDragCancel: () => {
      setDraggedItem(null);
    },
    onDragEnd: () => {
      setDraggedItem(null);
    },
  });

  if (!draggedItem) return null;

  let node: ReactNode = <div>No drag overlay</div>;
  const isMenuBarElement = draggedItem?.data?.current?.isMenuBarElement;
  if (isMenuBarElement) {
    const { elementName, elementIcon } = draggedItem?.data?.current as { elementName: string; elementIcon: StaticImageData };
    node = <MenuBarElement icon={elementIcon} name={elementName} isDragged />;
  }
  const screenId = draggedItem?.data?.current?.screenId;
  const sectionId = draggedItem?.data?.current?.sectionId;
  const isFormElement = draggedItem?.data?.current?.isFormElement;
  if (isFormElement) {
    const element = draggedItem.data.current?.element;
    if (!element) return;
    const Element = FIELD_COMPONENTS[element.type as ElementType];

    node = (
      <div className="bg-accent opacity-60">
        <FormElement key={element.id} element={element} Element={Element} screenId={screenId} sectionId={sectionId} />
      </div>
    );
  }

  const isFormSection = draggedItem?.data?.current?.isSection && !draggedItem?.id.toString().startsWith("menu-element");
  const section = draggedItem?.data?.current?.section;
  if (isFormSection) {
    const minHeight = Math.max(20, section.elements.length * 80);
    node = <SectionLayout section={section} screenId={screenId} />;
  }

  return <DragOverlay>{node}</DragOverlay>;
};

export default DragOverlayWrapper;
