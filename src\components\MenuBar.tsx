import { fredoka } from "@/app/fonts";
import AudioRecordingIcon from "@/assets/icons/audio-recording.svg";
import CameraIcon from "@/assets/icons/camera.svg";
import DateIcon from "@/assets/icons/date.svg";
import FileIcon from "@/assets/icons/file.svg";
import MultipleChoiceIcon from "@/assets/icons/multiple-choice.svg";
import NumberIcon from "@/assets/icons/number.svg";
import ParagraphIcon from "@/assets/icons/paragraph.svg";
import PhoneNumberIcon from "@/assets/icons/phone-number.svg";
import RatingsIcon from "@/assets/icons/ratings.svg";
import ReadOnlyIcon from "@/assets/icons/read-only.svg";
import SectionIcon from "@/assets/icons/section.svg";
import ShortAnswerIcon from "@/assets/icons/short-answer.svg";
import SingleChoiceIcon from "@/assets/icons/single-choice.svg";
import TimeIcon from "@/assets/icons/time.svg";
import type { MenuItems, MenuItemsOpen } from "@/components/types";
import { But<PERSON> } from "@/components/ui/button";
import { Fragment, useState } from "react";
import { VscTriangleRight } from "react-icons/vsc";
import MenuBarElement from "./MenuBarElement";
import DropdownIcon from "@/assets/icons/drop-down.svg";
import QRCodeScan from "@/assets/icons/qr-code-scan.svg";
import { cn } from "@/lib/utils";

const MenuBar = () => {
  const menuItems: MenuItems = [
    {
      id: 1,
      item: "Layout",
      subItems: [
        {
          id: "layout-1",
          icon: SectionIcon,
          title: "Section",
        },
      ],
    },
    {
      id: 2,
      item: "Basics",
      subItems: [
        {
          id: "basic-1",
          icon: ShortAnswerIcon,
          title: "Short Answer",
        },
        {
          id: "basic-2",
          icon: ParagraphIcon,
          title: "Paragraph",
        },
        {
          id: "basic-3",
          icon: NumberIcon,
          title: "Number",
        },
        {
          id: "basic-4",
          icon: PhoneNumberIcon,
          title: "Phone Number",
        },
        {
          id: "basic-5",
          icon: DateIcon,
          title: "Date",
        },
        {
          id: "basic-6",
          icon: TimeIcon,
          title: "Time",
        },
        {
          id: "basic-7",
          icon: SingleChoiceIcon,
          title: "Single Choice",
        },
        {
          id: "basic-8",
          icon: MultipleChoiceIcon,
          title: "Multiple Choice",
        },
        {
          id: "basic-9",
          icon: FileIcon,
          title: "File",
        },
        {
          id: "basic-10",
          icon: CameraIcon,
          title: "Camera",
        },
        {
          id: "basic-11",
          icon: AudioRecordingIcon,
          title: "Audio Recording",
        },
        {
          id: "basic-12",
          icon: RatingsIcon,
          title: "Rating",
        },
        {
          id: "basic-13",
          icon: DropdownIcon,
          title: "Dropdown",
        },
        {
          id: "basic-14",
          icon: QRCodeScan,
          title: "QR Code Scan",
        },
        {
          id: "basic-15",
          icon: ReadOnlyIcon,
          title: "Read Only",
        },
      ],
    },
  ];

  const [menuItemsOpen, setMenuItemsOpen] = useState<MenuItemsOpen>({ layout: true, basics: true });

  const handleMenuToggle = (type: "layout" | "basics") => {
    setMenuItemsOpen({
      ...menuItemsOpen,
      [type]: !menuItemsOpen[type],
    });
  };

  const getMenuType = (item: string): "layout" | "basics" => {
    return item.toLowerCase() as "layout" | "basics";
  };

  const toggleBtnBaseClass = "transition duration-200 ease-in-out";

  return (
    <aside className="fixed left-0 top-20 h-[calc(100vh-5rem)] w-[280px] overflow-y-auto bg-white p-4">
      <div className="flex items-center justify-between">
        <p className={`${fredoka.className} mb-4 text-lg font-semibold`}>Menu</p>
      </div>
      <div className="flex flex-col gap-4 pb-6">
        {menuItems.map(menuItem => {
          const menuType = getMenuType(menuItem.item);
          const isOpen = menuItemsOpen[menuType];
          return (
            <div key={menuItem.id}>
              <Button
                variant="ghost"
                className={`${fredoka.className} flex cursor-pointer items-center gap-1 p-0 text-lg font-semibold hover:bg-transparent`}
                onClick={() => handleMenuToggle(menuType)}
              >
                {menuItem.item}
                <VscTriangleRight size={12} className={cn(toggleBtnBaseClass, { "rotate-90": isOpen }, { "rotate-0": !isOpen })} />
              </Button>
              {isOpen &&
                menuItem.subItems.map(subItem => (
                  <Fragment key={subItem.id}>
                    <MenuBarElement icon={subItem.icon} name={subItem.title} />
                  </Fragment>
                ))}
            </div>
          );
        })}
      </div>
    </aside>
  );
};

export default MenuBar;
