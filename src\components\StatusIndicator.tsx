import OfflineIcon from "@/assets/icons/offline-indicator.svg";
import SavedIcon from "@/assets/icons/saved-indicator.svg";
import SavingIcon from "@/assets/icons/saving-indicator.svg";
import useAutosave from "@/hooks/use-autosave";
import useOnlineStatus from "@/hooks/use-online-status";
import Image from "next/image";
import { useEffect, useState } from "react";

const StatusIndicator = () => {
  const isOnline = useOnlineStatus();
  const { isSaving, isSaved } = useAutosave();
  const [showStatus, setShowStatus] = useState(true);

  useEffect(() => {
    if (isOnline) {
      setShowStatus(false);
    }

    if (isSaving || isSaved) {
      setShowStatus(true);
    }

    // If the status is "saved" and no other changes occur for 1 second, hide the status
    if (isSaved && !isSaving && isOnline) {
      const timer = setTimeout(() => {
        setShowStatus(false);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isOnline, isSaving, isSaved]);

  return (
    <div className={`flex items-center gap-2 ${showStatus || "invisible"}`}>
      <Image
        src={!isOnline ? OfflineIcon : isSaving ? SavingIcon : SavedIcon}
        alt={`${!isOnline ? "offline" : isSaving ? "saving" : "saved"} icon`}
        className="h-4 w-4"
      />
      <span className="text-sm text-muted-foreground">{!isOnline ? "You are offline" : isSaving ? "Saving..." : "Saved"}</span>
    </div>
  );
};

export default StatusIndicator;
