import { fredoka } from "@/app/fonts";
import BetterLifeLogo from "@/assets/images/better-life-logo.svg";
import DummyCompanyLogo from "@/assets/images/dummy-company-logo.svg";
import Image from "next/image";
import { TopBarProps } from "@/components/types";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAppSelector } from "@/hooks/use-redux";
import StatusIndicator from "./StatusIndicator";

const TopBar = ({ pageTitle, organization }: TopBarProps) => {
  const organizationLogo = organization?.logo;
  const organizationFallback = organization?.name
    .split(" ")
    .map(word => word[0])
    .join("")
    .slice(0, 2);

  return (
    <div className="fixed left-0 top-0 z-10 flex h-20 w-full items-center justify-between bg-white p-4">
      <Avatar>
        <AvatarImage src={organizationLogo} />
        <AvatarFallback>{organizationFallback}</AvatarFallback>
      </Avatar>
      <div className={`${fredoka.className} relative text-lg font-semibold`}>
        {pageTitle}
        <div className="absolute right-0 top-0 translate-x-full translate-y-1/4 pl-2">
          <StatusIndicator />
        </div>
      </div>
      <Image src={BetterLifeLogo} alt="Better Life Image" className="h-8 w-8" />
    </div>
  );
};

export default TopBar;
