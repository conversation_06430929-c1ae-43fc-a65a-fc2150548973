import { DropdownPropertiesFormData } from "@/app/(core)/forms/types";
import AddIcon from "@/assets/icons/add-option.svg";
import RemoveIcon from "@/assets/icons/remove-option.svg";
import ReorderIcon from "@/assets/icons/reorder.svg";
import LogicModal from "@/components/dialogs/LogicModal";
import { FormElement } from "@/components/types";
import { useAppSelector } from "@/hooks/use-redux";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId, updateFormElementOnInputChange } from "@/lib/utils";
import { dropdownPropertiesSchema } from "@/schemas/properties/dropdownProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const DropdownProperties = () => {
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const { options = [] } = selectedFormBuilderItem as FormElement;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const form = useForm<DropdownPropertiesFormData>({
    resolver: zodResolver(dropdownPropertiesSchema),
    mode: "onChange",
    defaultValues: {
      isCorrectAnswerTicked: selectedFormBuilderItem?.isCorrectAnswerTicked,
      correctAnswer: selectedFormBuilderItem?.correctAnswer,
    },
    shouldFocusError: false,
  });

  const { setValue } = form;

  useEffect(() => {
    setValue("isCorrectAnswerTicked", selectedFormBuilderItem?.isCorrectAnswerTicked);
    setValue("correctAnswer", selectedFormBuilderItem?.correctAnswer);
  }, [selectedFormBuilderItem]);

  const handleAddOption = (index: number) => {
    const newOptions = [...options];
    newOptions?.splice(index + 1, 0, "");
    updateFormElementOnInputChange({ options: newOptions }, screenId, sectionId);
  };

  const handleRemoveOption = (index: number) => {
    if (options.length === 1) return;
    const newOptions = [...options];
    newOptions?.splice(index, 1);
    updateFormElementOnInputChange({ options: newOptions }, screenId, sectionId);
  };

  return (
    <div>
      <p className="mb-2">Options</p>
      <div className="space-y-2">
        {selectedFormBuilderItem?.options?.map((option, i) => (
          <div key={i} className="flex items-center gap-2">
            <div className="flex h-[3rem] w-[8.4rem] items-center overflow-hidden rounded-[10px] border border-primary-gray bg-primary-gray/10 p-3 text-primary-gray">
              {option || `Option ${i + 1}`}
            </div>
            <div className="flex items-center gap-2">
              <Image src={AddIcon} alt="Add Option" className="h-6 w-6 cursor-pointer" onClick={() => handleAddOption(i)} />
              <Image
                src={RemoveIcon}
                alt="Remove Option"
                className={`h-6 w-6 ${options.length > 1 ? "cursor-pointer" : "cursor-not-allowed"}`}
                onClick={() => handleRemoveOption(i)}
              />
              <Image src={ReorderIcon} alt="Remove Option" className="h-6 w-6 cursor-pointer" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DropdownProperties;
