import { QrCodeScanPropertiesFormData } from "@/app/(core)/forms/types";
import deleteIcon from "@/assets/icons/delete-icon.svg";
import plusIcon from "@/assets/icons/plus-green.svg";
import DatePicker from "@/components/DatePicker";
import NumberInput from "@/components/NumberInput";
import { FormElement } from "@/components/types";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useGetSingleForm } from "@/hooks/tansack-query/queries/use-forms";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { QR_CODE_PATTERN_TYPE, QR_CODE_RULE_TYPE, QR_CODE_VALIDATION_TYPE } from "@/lib/constants";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId, generateId } from "@/lib/utils";
import { qrCodeScanPropertiesSchema } from "@/schemas/properties/qrCodeScanProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import * as changeCase from "change-case";
import Image from "next/image";
import { useEffect } from "react";
import { useFieldArray, useForm } from "react-hook-form";

const QrCodeScanProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const formId = useAppSelector(state => state.form.formId);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const { singleFormData } = useGetSingleForm(formId);
  const validationTypes = ["Allow all types", "Text", "Number", "Email", "URL", "Phone Number", "Date"];
  const ruleTypes = ["Length", "Pattern"];
  const patternTypes = ["Contains", "Equal to", "Starts with", "Ends with"];

  const form = useForm<QrCodeScanPropertiesFormData>({
    resolver: zodResolver(qrCodeScanPropertiesSchema),
    mode: "onChange",
    defaultValues: {
      validationType: selectedFormBuilderItem?.validationType,
      rules: selectedFormBuilderItem?.rules || [],
    },
    shouldFocusError: false,
  });

  const {
    setValue,
    watch,
    control,
    formState: { errors },
  } = form;

  const {
    fields: rulesFields,
    append: appendRule,
    remove: removeRule,
  } = useFieldArray({
    control,
    name: "rules",
  });

  const handleAddRule = () => {
    form.trigger("rules");
    appendRule({
      id: `rule_${generateId()}`,
      type: QR_CODE_RULE_TYPE.length,
    });
  };

  useEffect(() => {
    setValue("validationType", selectedFormBuilderItem.validationType as string);
    setValue("rules", selectedFormBuilderItem.rules || []);

    // Reset the form fields to match the new selected element
    // form.reset({
    //   validationType: selectedFormBuilderItem.validationType as string,
    //   rules: selectedFormBuilderItem.rules || [],
    // });
  }, [selectedFormBuilderItem]);

  const updateFormElements = (data: QrCodeScanPropertiesFormData) => {
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data,
    } as FormElement;

    if (data.validationType !== QR_CODE_VALIDATION_TYPE.number) {
      delete newFormElement["minimumValue"];
      delete newFormElement["maximumValue"];
      delete newFormElement["allowDecimals"];
      delete newFormElement["currency"];
    }

    if (data.validationType !== QR_CODE_VALIDATION_TYPE.phoneNumber) {
      delete newFormElement["minimumLength"];
      delete newFormElement["maximumLength"];
    }

    if (data.validationType !== QR_CODE_VALIDATION_TYPE.date) {
      delete newFormElement["includeTimeValidation"];
      delete newFormElement["minimumDateRange"];
      delete newFormElement["maximumDateRange"];
    }

    if (data.validationType !== QR_CODE_VALIDATION_TYPE.text) {
      delete newFormElement["rules"];
    }

    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(updateFormElements)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="validationType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Validation Type</FormLabel>
                <Select value={field.value} onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {validationTypes.map(type => (
                      <SelectItem value={changeCase.snakeCase(type)} key={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          {watch("validationType") === QR_CODE_VALIDATION_TYPE.number && (
            <>
              <p>Values</p>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="minimumValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Min"
                          onChange={field.onChange}
                          className={`${errors.minimumValue && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="maximumValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Max"
                          onChange={field.onChange}
                          className={`${errors.maximumValue && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="allowDecimals"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Allow decimal numbers</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Currency</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </>
          )}
          {watch("validationType") === QR_CODE_VALIDATION_TYPE.phoneNumber && (
            <>
              <p>Length</p>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="minimumLength"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Min Digits"
                          onChange={field.onChange}
                          className={`${errors.minimumLength && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="maximumLength"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <NumberInput
                          {...field}
                          value={field.value}
                          placeholder="Max Digits"
                          onChange={field.onChange}
                          className={`${errors.maximumLength && "border-destructive"}`}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <p>Country Code</p>
              <div className="h-[3rem] rounded-[10px] border border-primary-gray bg-primary-gray/10 p-3">{singleFormData?.country?.phone_code}</div>
            </>
          )}
          {watch("validationType") === QR_CODE_VALIDATION_TYPE.date && (
            <>
              <FormField
                control={form.control}
                name="includeTimeValidation"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-1">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel>Include time validation</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="minimumDateRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Date</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        onChange={date => {
                          if (date) {
                            field.onChange(date.toISOString());
                          } else {
                            field.onChange(undefined);
                          }
                          form.trigger("minimumDateRange");
                          form.trigger("maximumDateRange");
                        }}
                        className={`w-full ${errors.minimumDateRange && "border-destructive"}`}
                        placeholder="dd/mm/yyyy"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="maximumDateRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maximum Date</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        onChange={date => {
                          if (date) {
                            field.onChange(date.toISOString());
                          } else {
                            field.onChange(undefined);
                          }
                          form.trigger("minimumDateRange");
                          form.trigger("maximumDateRange");
                        }}
                        className={`w-full ${errors.maximumDateRange && "border-destructive"}`}
                        placeholder="dd/mm/yyyy"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
          {watch("validationType") === QR_CODE_VALIDATION_TYPE.text && (
            <div className="">
              <Button
                type="button"
                variant="ghost"
                className={"cursor-pointer items-center gap-2 p-0 font-normal hover:bg-transparent"}
                onClick={handleAddRule}
              >
                <Image src={plusIcon} alt="Plus Icon" width={14} height={14} />
                Add Rule
              </Button>
              {rulesFields.map((rule, ruleIndex) => (
                <div key={rule.id}>
                  <p>Rule {ruleIndex + 1}</p>
                  <div className="mt-2 space-y-4">
                    <FormField
                      control={form.control}
                      name={`rules.${ruleIndex}.type`}
                      render={({ field }) => (
                        <FormItem>
                          <Select
                            value={field.value}
                            onValueChange={value => {
                              field.onChange(value);
                              if (value === QR_CODE_RULE_TYPE.length) {
                                // Clear pattern-related fields when switching to length
                                form.setValue(`rules.${ruleIndex}.patternType`, undefined);
                                form.setValue(`rules.${ruleIndex}.patternText`, undefined);
                                // Clear character count fields to start fresh
                                form.setValue(`rules.${ruleIndex}.minimumCharacterCount`, undefined);
                                form.setValue(`rules.${ruleIndex}.maximumCharacterCount`, undefined);
                                form.setValue(`rules.${ruleIndex}.exactCharacterCount`, undefined);
                              }

                              if (value === QR_CODE_RULE_TYPE.pattern) {
                                form.setValue(`rules.${ruleIndex}.patternType`, QR_CODE_PATTERN_TYPE.contains);
                                // Clear character count fields when switching to pattern
                                form.setValue(`rules.${ruleIndex}.minimumCharacterCount`, undefined);
                                form.setValue(`rules.${ruleIndex}.maximumCharacterCount`, undefined);
                                form.setValue(`rules.${ruleIndex}.exactCharacterCount`, undefined);
                                // Clear pattern text to start fresh
                                form.setValue(`rules.${ruleIndex}.patternText`, undefined);
                              }
                            }}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {ruleTypes.map(type => (
                                <SelectItem value={changeCase.snakeCase(type)} key={type}>
                                  {type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />

                    {watch(`rules.${ruleIndex}.type`) === QR_CODE_RULE_TYPE.length && (
                      <>
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name={`rules.${ruleIndex}.minimumCharacterCount`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <NumberInput
                                    {...field}
                                    value={field.value}
                                    placeholder="Min"
                                    onChange={field.onChange}
                                    className={`${errors.rules?.[ruleIndex]?.minimumCharacterCount && "border-destructive"}`}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name={`rules.${ruleIndex}.maximumCharacterCount`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <NumberInput
                                    {...field}
                                    value={field.value}
                                    placeholder="Max"
                                    onChange={field.onChange}
                                    className={`${errors.rules?.[ruleIndex]?.maximumCharacterCount && "border-destructive"}`}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <FormField
                          control={form.control}
                          name={`rules.${ruleIndex}.exactCharacterCount`}
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <NumberInput
                                  {...field}
                                  value={field.value}
                                  placeholder="Exact Character Count"
                                  onChange={field.onChange}
                                  className={`${errors.rules?.[ruleIndex]?.exactCharacterCount && "border-destructive"}`}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <span className="text-xs">This will override min/max if specified</span>
                      </>
                    )}

                    {watch(`rules.${ruleIndex}.type`) === QR_CODE_RULE_TYPE.pattern && (
                      <div className="space-y-4">
                        <FormField
                          control={form.control}
                          name={`rules.${ruleIndex}.patternType`}
                          render={({ field }) => (
                            <FormItem>
                              <Select value={field.value} onValueChange={field.onChange}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select pattern type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {patternTypes.map(type => (
                                    <SelectItem value={changeCase.snakeCase(type)} key={type}>
                                      {type}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`rules.${ruleIndex}.patternText`}
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input
                                  {...field}
                                  value={field.value || ""}
                                  placeholder="Enter pattern text"
                                  className={`${errors.rules?.[ruleIndex]?.patternText && "border-destructive"}`}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    className="cursor-pointer items-center gap-2 p-0 font-normal text-destructive hover:bg-transparent hover:text-destructive"
                    onClick={() => removeRule(ruleIndex)}
                  >
                    <Image src={deleteIcon} alt="Delete Icon" width={14} height={14} />
                    Delete Rule
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </form>
    </Form>
  );
};

export default QrCodeScanProperties;
