import { RatingPropertiesFormData } from "@/app/(core)/forms/types";
import LogicModal from "@/components/dialogs/LogicModal";
import { FormElement } from "@/components/types";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { NUMBERS_OF_RATINGS } from "@/lib/constants";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId } from "@/lib/utils";
import { ratingPropertiesSchema } from "@/schemas/properties/ratingProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const RatingProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const form = useForm<RatingPropertiesFormData>({
    resolver: zodResolver(ratingPropertiesSchema),
    mode: "onChange",
    defaultValues: {
      numbersOfRating: selectedFormBuilderItem?.numbersOfRating || 10,
    },
    shouldFocusError: false,
  });

  const {
    setValue,
    formState: { errors },
  } = form;

  useEffect(() => {
    setValue("numbersOfRating", selectedFormBuilderItem?.numbersOfRating || 10);
  }, [selectedFormBuilderItem]);

  const applyChanges = (data: RatingPropertiesFormData) => {
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  const handleRatingChange = (value: string) => {
    setValue("numbersOfRating", Number(value)); // Convert to number
    applyChanges({ numbersOfRating: Number(value) });
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(applyChanges)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="numbersOfRating"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Numbers of Rating</FormLabel>
                <FormControl>
                  <Select onValueChange={handleRatingChange} value={String(field.value)}>
                    <SelectTrigger className="placeholder:text-primary-gray">
                      <SelectValue placeholder="10">{field.value}</SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: NUMBERS_OF_RATINGS }, (_, i) => NUMBERS_OF_RATINGS - i).map(code => (
                        <SelectItem key={code} value={String(code)}>
                          {code}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
};

export default RatingProperties;
