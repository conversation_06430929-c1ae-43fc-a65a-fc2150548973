"use client";
import { fredoka } from "@/app/fonts";
import bellIcon from "@/assets/icons/bell.svg";
import { CreateFormDialogProps } from "@/components/types";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { PRIMARY_COLOUR_GREEN } from "@/lib/constants";
import { closeCreateFormDialog } from "@/lib/redux/slices/dialogSlice";
import Image from "next/image";
import { useRouter } from "next/navigation";
import ClipLoader from "react-spinners/ClipLoader";

const CreateFormDialog = ({ isCreatingBlankForm, handleOpenExistingForm, handleCreateBlankForm }: CreateFormDialogProps) => {
  const isCreateFormDialogOpen = useAppSelector(state => state.dialog.isCreateFormDialogOpen);

  return (
    <AlertDialog open={isCreateFormDialogOpen}>
      <AlertDialogContent className="flex min-h-80 flex-col items-center justify-center gap-2 px-8">
        <AlertDialogHeader className="mb-5 space-y-2">
          <AlertDialogTitle className={`gap-4font-semibold mb-3 flex flex-col items-center justify-center text-lg ${fredoka.className}`}>
            <Image src={bellIcon} alt="Better Life Image" width={48} height={48} />
            <p>Confirm Option</p>
          </AlertDialogTitle>
          <AlertDialogDescription className="text-center text-base text-[#1E202C]">
            Do you want to create a blank form or select a template from an existing form?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex items-center gap-4">
          <Button
            variant="outline"
            className={`${fredoka.className} h-10 w-[7.7rem] rounded-[10px] px-2 text-lg font-semibold`}
            onClick={handleOpenExistingForm}
          >
            Existing Form
          </Button>
          <Button
            variant="outline"
            className={`${fredoka.className} h-10 w-[7.7rem] rounded-[10px] px-2 text-lg font-semibold disabled:border-primary-green`}
            disabled={isCreatingBlankForm}
            onClick={handleCreateBlankForm}
          >
            {isCreatingBlankForm ? <ClipLoader color={PRIMARY_COLOUR_GREEN} size={20} /> : "Blank Form"}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default CreateFormDialog;
