import { fredoka } from "@/app/fonts";
import LogicItem from "@/components/logic/LogicItem";
import { ElementLogicData, FormElement, FormSection, LogicModalProps } from "@/components/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { closeLogicModal } from "@/lib/redux/slices/dialogSlice";
import { replaceFormElement, replaceFormSection, updateSelectedFormBuilderItem } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload, SelectedFormSectionPayload } from "@/lib/redux/types";
import { generateId } from "@/lib/utils";
import { elementLogicSchema } from "@/schemas/logic/logic";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useFieldArray, useForm } from "react-hook-form";

const LogicModal = ({ screenId, sectionId }: LogicModalProps) => {
  const dispatch = useAppDispatch();
  const isLogicModalOpen = useAppSelector(state => state.dialog.isLogicModalOpen);
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as
    | SelectedFormElementPayload
    | SelectedFormSectionPayload;

  const defaultLogic = {
    id: `logic_${generateId()}`,
    conditions: [
      {
        id: `condition_${generateId()}`,
        componentId: "",
        operator: "",
        value: "",
        logic: null,
      },
    ],
    action: "show",
  };

  const applyChanges = (data: ElementLogicData) => {
    const clonedLogics = data.logics ? deepCloneLogics(data.logics) : [];

    if (selectedFormBuilderItem?.type === "SectionLayout") {
      const { type, ...itemData } = selectedFormBuilderItem;
      const newFormSection = {
        ...itemData,
        ...data,
        logics: clonedLogics,
      } as FormSection;
      dispatch(replaceFormSection({ screenId, section: newFormSection }));

      const updatedSelectedItem = {
        ...selectedFormBuilderItem,
        ...data,
        logics: clonedLogics,
      } as SelectedFormSectionPayload;
      dispatch(updateSelectedFormBuilderItem(updatedSelectedItem));
    } else {
      const newFormElement = {
        ...selectedFormBuilderItem,
        ...data,
        logics: clonedLogics,
      } as FormElement;
      dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));

      const updatedSelectedItem = {
        ...selectedFormBuilderItem,
        ...data,
        logics: clonedLogics,
      } as SelectedFormElementPayload;
      dispatch(updateSelectedFormBuilderItem(updatedSelectedItem));
    }
  };

  const handleOpenChange = (open: boolean): void => {
    if (!open) {
      const currentData = form.getValues();
      applyChanges(currentData);
      dispatch(closeLogicModal());
    }
  };

  const deepCloneLogics = (logics: any[]) => {
    return logics.map(logic => ({
      ...logic,
      conditions: logic.conditions ? [...logic.conditions.map((condition: any) => ({ ...condition }))] : [],
    }));
  };

  const getDefaultValues = () => {
    const isLogicPresent = selectedFormBuilderItem?.logics?.length;
    return {
      logics: isLogicPresent ? deepCloneLogics(selectedFormBuilderItem.logics || []) : [defaultLogic],
    };
  };

  const form = useForm<ElementLogicData>({
    resolver: zodResolver(elementLogicSchema),
    mode: "onChange",
    defaultValues: getDefaultValues(),
  });

  const { control, reset } = form;

  useEffect(() => {
    if (isLogicModalOpen) {
      reset(getDefaultValues());
    }
  }, [isLogicModalOpen, selectedFormBuilderItem, reset]);

  const {
    fields: logicFields,
    append: appendLogic,
    remove: removeLogic,
  } = useFieldArray({
    control,
    name: "logics",
  });

  const handleAddLogic = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    appendLogic(defaultLogic);
  };

  const handleDeleteLogic = (logicIndex: number) => {
    removeLogic(logicIndex);
    const currentData = form.getValues();
    applyChanges(currentData);

    // If this was the last logic item, ensure selectedFormBuilderItem reflects no logic
    if (logicFields.length === 1) {
      const updatedItemWithNoLogic = {
        ...selectedFormBuilderItem,
        logics: [],
      };
      if (selectedFormBuilderItem?.type === "SectionLayout") {
        dispatch(updateSelectedFormBuilderItem(updatedItemWithNoLogic as SelectedFormSectionPayload));
      } else {
        dispatch(updateSelectedFormBuilderItem(updatedItemWithNoLogic as SelectedFormElementPayload));
      }
      dispatch(closeLogicModal());
    }
  };

  return (
    <Dialog open={isLogicModalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="w-[60rem] px-8">
        <DialogHeader>
          <DialogTitle className={`${fredoka.className} text-lg font-semibold`}>Logic</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form className="max-h-[30rem] space-y-4 overflow-auto">
            {logicFields.map((logicField, logicIndex) => (
              <LogicItem key={logicField.id} logicIndex={logicIndex} logicField={logicField} form={form} handleDeleteLogic={handleDeleteLogic} />
            ))}
          </form>
        </Form>
        <div>
          <Button className={`${fredoka.className} font-semibold`} type="button" onClick={handleAddLogic}>
            Add Logic
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LogicModal;
