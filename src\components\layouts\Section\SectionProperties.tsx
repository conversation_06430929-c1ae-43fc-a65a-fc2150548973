import { fredoka } from "@/app/fonts";
import mergeIcon from "@/assets/icons/merge-icon.svg";
import LogicButton from "@/components/LogicButton";
import { FormSection } from "@/components/types";
import { Button } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { addFormSection, replaceFormScreens, updateSelectedFormBuilderItem } from "@/lib/redux/slices/formSlice";
import { SelectedFormSectionPayload } from "@/lib/redux/types";
import { generateId, transformElementType } from "@/lib/utils";
import Image from "next/image";
import { useState } from "react";
import { VscTriangleRight } from "react-icons/vsc";

const SectionProperties = () => {
  const [isGeneralOpen, setIsGeneralOpen] = useState<boolean>(true);
  const dispatch = useAppDispatch();
  const formScreens = useAppSelector(state => state.form.formScreens);
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormSectionPayload;
  const selectedFormBuilderItemScreen = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const currentScreen = formScreens.find(screen => screen.id === selectedFormBuilderItemScreen);
  if (!currentScreen) return;
  const currentScreenIndex = formScreens.findIndex(screen => screen.id === selectedFormBuilderItemScreen);
  const currentSectionIndex = currentScreen.sections.findIndex(section => section.id === selectedFormBuilderItem?.id);
  const sectionId = selectedFormBuilderItem?.id || "";
  const isLogicPresent = !!selectedFormBuilderItem?.logics?.length;

  const handleDuplicateSection = () => {
    if (currentSectionIndex === -1) {
      throw new Error("Section not found");
    }
    const duplicatedSection = {
      id: `section_${generateId()}`,
      title: selectedFormBuilderItem.title,
      elements: selectedFormBuilderItem.elements.map(element => ({
        ...element,
        id: `${transformElementType(element.type)}_${generateId()}`,
      })),
    } as FormSection;
    dispatch(addFormSection({ screenId: currentScreen.id, sectionIndex: currentSectionIndex + 1, section: duplicatedSection }));
  };

  const handleMergeSections = () => {
    const newScreens = formScreens.map((screen, screenIndex) => {
      return {
        ...screen,
        sections: screen.sections
          .map((section, sectionIndex) => {
            if (screenIndex === currentScreenIndex && sectionIndex === currentSectionIndex - 1) {
              return {
                ...section,
                elements: [...section.elements, ...currentScreen.sections[currentSectionIndex].elements],
              };
            }
            return section;
          })
          .filter(section => section.id !== sectionId),
      };
    });
    dispatch(replaceFormScreens(newScreens));
  };

  const handleDeleteSection = () => {
    if (currentScreen?.sections.length === 1) return;
    const newFormScreens = formScreens.map(screen => ({
      ...screen,
      sections: screen.sections.filter(section => section.id !== sectionId),
    }));
    dispatch(replaceFormScreens(newFormScreens));
    dispatch(updateSelectedFormBuilderItem({ id: formScreens[0]?.id, type: "ScreenLayout" }));
  };

  return (
    <div>
      <div>
        <LogicButton isLogicPresent={isLogicPresent} screenId={selectedFormBuilderItemScreen} sectionId={sectionId} />
        <Button
          variant="ghost"
          className={`${fredoka.className} mt-4 flex cursor-pointer items-center gap-1 p-0 text-lg font-semibold hover:bg-transparent`}
          onClick={() => setIsGeneralOpen(!isGeneralOpen)}
        >
          <VscTriangleRight className={`${isGeneralOpen ? "rotate-90" : "rotate-0"} transition duration-200 ease-in-out`} /> General
        </Button>
        {isGeneralOpen && (
          <Button
            variant="ghost"
            className={`ml-4 flex items-center gap-2 p-0 hover:bg-transparent ${currentSectionIndex === 0 ? "cursor-not-allowed" : "cursor-pointer"}`}
            onClick={handleMergeSections}
          >
            <Image src={mergeIcon} alt="Merge" width={14} height={14} />
            <p>Merge Section</p>
          </Button>
        )}
      </div>
      <div className="mt-8 flex flex-col gap-2">
        <Button className={`h-[3.4rem] w-full ${fredoka.className} text-lg font-semibold`} variant="outline" onClick={handleDuplicateSection}>
          Duplicate Section
        </Button>
        <Button
          className={`h-[3.4rem] w-full ${fredoka.className} text-lg font-semibold`}
          variant="outline-red"
          disabled={currentScreen?.sections.length === 1}
          onClick={handleDeleteSection}
        >
          Delete Section
        </Button>
      </div>
    </div>
  );
};

export default SectionProperties;
