import DatePicker from "@/components/DatePicker";
import NumberInput from "@/components/NumberInput";
import { ParentAnswerFieldProps } from "@/components/types";
import { FormControl, FormField, FormItem } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const ConditionValueField = ({ logicIndex, conditionIndex, type, options, form }: ParentAnswerFieldProps) => {
  const isChoiceField = ["SingleChoiceField", "MultipleChoiceField", "DropdownField"].includes(type);
  const isDateField = type === "DateField";
  const currentOperator = form.watch(`logics.${logicIndex}.conditions.${conditionIndex}.operator`);
  const isRangeOperator = currentOperator === "is in range";

  const getFieldName = (suffix: string) => `logics.${logicIndex}.conditions.${conditionIndex}.${suffix}`;

  const handleDateChange = (fieldName: string, date: Date | undefined) => {
    if (date) {
      form.setValue(fieldName as any, date.toISOString(), { shouldValidate: true });
    }
  };

  const renderDatePicker = (fieldName: string, value: any) => (
    <DatePicker
      value={value ? new Date(value) : undefined}
      onChange={date => handleDateChange(fieldName, date)}
      disabled={!currentOperator}
      className={isRangeOperator ? undefined : "w-full"}
    />
  );

  const renderNumberInput = (field: any) => (
    <NumberInput
      {...field}
      placeholder={isRangeOperator ? (field.name.includes("minimum") ? "Min Number" : "Max Number") : "Enter value"}
      onChange={field.onChange}
      disabled={!currentOperator}
    />
  );

  const renderChoiceSelect = (field: any) => (
    <Select onValueChange={field.onChange} value={field.value} disabled={!currentOperator}>
      <SelectTrigger>
        <SelectValue placeholder="Select Option" />
      </SelectTrigger>
      <SelectContent>
        {options.map(option => (
          <SelectItem key={option} value={option}>
            {option}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );

  const renderSingleField = (fieldName: string, field: any) => {
    if (isChoiceField) return renderChoiceSelect(field);
    if (isDateField) return renderDatePicker(fieldName, field.value);
    return renderNumberInput(field);
  };

  const renderRangeFields = () => (
    <div className="flex gap-2">
      <FormField
        control={form.control}
        name={getFieldName("minimumValue")}
        render={({ field }) => (
          <FormItem className="flex-1">
            <FormControl>{isDateField ? renderDatePicker(getFieldName("minimumValue"), field.value) : renderNumberInput(field)}</FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name={getFieldName("maximumValue")}
        render={({ field }) => (
          <FormItem className="flex-1">
            <FormControl>{isDateField ? renderDatePicker(getFieldName("maximumValue"), field.value) : renderNumberInput(field)}</FormControl>
          </FormItem>
        )}
      />
    </div>
  );

  const renderSingleValueField = () => (
    <FormField
      control={form.control}
      name={getFieldName("value")}
      render={({ field }) => (
        <FormItem>
          <FormControl>{renderSingleField(getFieldName("value"), field)}</FormControl>
        </FormItem>
      )}
    />
  );

  return isRangeOperator ? renderRangeFields() : renderSingleValueField();
};

export default ConditionValueField;
