import deleteIcon from "@/assets/icons/delete-icon.svg";
import plusIcon from "@/assets/icons/plus-green-outline.svg";
import ConditionValueField from "@/components/logic/ConditionValueField";
import { LogicItemProps } from "@/components/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useAppSelector } from "@/hooks/use-redux";
import { SelectedFormElementPayload, SelectedFormSectionPayload } from "@/lib/redux/types";
import { generateId } from "@/lib/utils";
import Image from "next/image";
import { useFieldArray } from "react-hook-form";

const LogicItem = ({ logicIndex, logicField, form, handleDeleteLogic }: LogicItemProps) => {
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as
    | SelectedFormElementPayload
    | SelectedFormSectionPayload;
  const actionQuestion =
    selectedFormBuilderItem.type === "SectionLayout" ? selectedFormBuilderItem.title || "This Section" : selectedFormBuilderItem.label;
  const formScreens = useAppSelector(state => state.form.formScreens);
  const fieldsWithLogic = ["NumberField", "DateField", "SingleChoiceField", "MultipleChoiceField", "DropdownField", "RatingField"];

  const allQuestions = formScreens
    .flatMap(screen => screen.sections)
    .flatMap(section => section.elements)
    .map(element => ({ id: element.id, label: element.label, type: element.type, options: element.options }))
    .filter(element => element.id !== selectedFormBuilderItem.id)
    .filter(element => !!element.label); // Get all questions on all elements in all sections and screens

  const logicOptions = ["and", "or"];

  const getOperatorsForQuestionType = (componentType: string) => {
    const operatorMap: Record<string, string[]> = {
      NumberField: [
        "is equal to",
        "is not equal to",
        "is lower than",
        "is greater than",
        "is lower than or equal to",
        "is greater than or equal to",
        "is in range",
      ],
      DateField: ["is on", "is not on", "is before", "is after", "is before or on", "is after or on", "is in range"],
      SingleChoiceField: ["is", "is not"],
      MultipleChoiceField: ["is", "is not"],
      DropdownField: ["is", "is not"],
      RatingField: [
        "is equal to",
        "is not equal to",
        "is lower than",
        "is greater than",
        "is lower than or equal to",
        "is greater than or equal to",
        "is in range",
      ],
    };

    return operatorMap[componentType] || [];
  };

  const {
    fields: conditionFields,
    append: appendCondition,
    remove: removeCondition,
  } = useFieldArray({
    control: form.control,
    name: `logics.${logicIndex}.conditions`,
  });

  const handleAddCondition = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    appendCondition({
      id: `condition_${generateId()}`,
      componentId: "",
      operator: "",
      value: "",
      logic: "and",
    });
  };

  const handleDeleteCondition = (e: React.MouseEvent<HTMLButtonElement>, conditionIndex: number) => {
    e.preventDefault();
    removeCondition(conditionIndex);
  };

  return (
    <div key={logicField.id} className="rounded-[10px] border border-[#75748F] p-4">
      <div className="flex flex-col gap-2">
        {conditionFields.map((conditionField, conditionIndex) => {
          const componentId = form.watch(`logics.${logicIndex}.conditions.${conditionIndex}.componentId`);
          const componentType = allQuestions.find(question => question.id === componentId)?.type ?? "";
          const componentOptions = allQuestions.find(question => question.id === componentId)?.options ?? [];

          return (
            <div key={conditionField.id} className="space-y-4">
              <div className={`w-[10rem] ${conditionField.logic !== null ? "block" : "hidden"}`}>
                <FormField
                  control={form.control}
                  name={`logics.${logicIndex}.conditions.${conditionIndex}.logic`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value ?? ""}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {logicOptions.map(option => (
                              <SelectItem key={option} value={option}>
                                {option}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name={`logics.${logicIndex}.conditions.${conditionIndex}.componentId`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={`${conditionIndex === 0 ? "block" : "hidden"}`}>If</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={value => {
                          field.onChange(value);
                          form.setValue(`logics.${logicIndex}.conditions.${conditionIndex}.operator`, "");
                          form.setValue(`logics.${logicIndex}.conditions.${conditionIndex}.value`, "");
                        }}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Parent Question" />
                        </SelectTrigger>
                        <SelectContent>
                          {allQuestions.map(question => (
                            <SelectItem key={question.id} value={question.id}>
                              {question.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </FormItem>
                )}
              />
              <div className="flex gap-4">
                <div className="w-[20rem]">
                  <FormField
                    control={form.control}
                    name={`logics.${logicIndex}.conditions.${conditionIndex}.operator`}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Select
                            onValueChange={value => {
                              field.onChange(value);
                              if (value === "is in range") {
                                form.unregister(`logics.${logicIndex}.conditions.${conditionIndex}.value`);
                                form.setValue(`logics.${logicIndex}.conditions.${conditionIndex}.minimumValue`, "");
                                form.setValue(`logics.${logicIndex}.conditions.${conditionIndex}.maximumValue`, "");
                              } else {
                                form.setValue(`logics.${logicIndex}.conditions.${conditionIndex}.value`, "");
                                form.unregister(`logics.${logicIndex}.conditions.${conditionIndex}.minimumValue`);
                                form.unregister(`logics.${logicIndex}.conditions.${conditionIndex}.maximumValue`);
                              }
                            }}
                            value={field.value}
                            disabled={!componentType || !fieldsWithLogic.includes(componentType)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {getOperatorsForQuestionType(componentType).map((operator: string) => (
                                <SelectItem key={operator} value={operator}>
                                  {operator}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-full">
                  <ConditionValueField
                    logicIndex={logicIndex}
                    conditionIndex={conditionIndex}
                    type={componentType}
                    options={componentOptions}
                    form={form}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-3">
                <Button
                  variant="ghost"
                  className={`cursor-pointer items-center gap-2 p-0 font-normal hover:bg-transparent ${conditionIndex === conditionFields.length - 1 ? "flex" : "hidden"}`}
                  onClick={handleAddCondition}
                >
                  <Image src={plusIcon} alt="Plus Icon" width={14} height={14} />
                  Add Condition
                </Button>
                <Button
                  variant="ghost"
                  className={`cursor-pointer items-center gap-2 p-0 font-normal hover:bg-transparent ${conditionIndex > 0 && conditionFields.length > 1 ? "flex" : "hidden"}`}
                  onClick={e => handleDeleteCondition(e, conditionIndex)}
                >
                  <Image src={deleteIcon} alt="Delete Icon" width={14} height={14} />
                  Delete Condition
                </Button>
              </div>
            </div>
          );
        })}
        <Separator className="my-2" />
        <div className="flex items-center gap-4">
          <div className="w-[10rem]">
            <FormField
              control={form.control}
              name={`logics.${logicIndex}.action`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Then</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="show">Show</SelectItem>
                        <SelectItem value="hide">Hide</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className="w-full space-y-2">
            <FormLabel className="invisible">*</FormLabel>
            <Input value={actionQuestion} readOnly />
          </div>
        </div>
        <div className="mt-4 flex justify-end gap-3">
          <Button
            variant="ghost"
            className={"cursor-pointer items-center gap-2 p-0 font-normal hover:bg-transparent"}
            onClick={() => handleDeleteLogic(logicIndex)}
          >
            <Image src={deleteIcon} alt="Delete Icon" width={14} height={14} />
            Delete Logic
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LogicItem;
