import { ElementType, FormElement, FormScreen, FormSection } from "@/components/types";

export type AuthState = {
  userTokens: {
    accessToken: string;
    refreshToken: string;
  };
  isSomethingWrong: boolean;
};

export type DialogState = {
  isConfirmationDialogOpen: boolean;
  isCreateFormDialogOpen: boolean;
  isSuccessDialogOpen: boolean;
  successMessage: string;
  confirmationMessage: string;
  isLogicModalOpen: boolean;
};

export type FormState = {
  formScreens: FormScreen[];
  formId: string;
  selectedFormBuilderItem: SelectedFormElementPayload | SelectedFormSectionPayload | null;
  selectedFormBuilderItemScreen: string;
};

export type AddFormElementPayload = {
  screenId: string;
  sectionId: string;
  elementIndex: number;
  element: FormElement;
};

export type RemoveFormElementPayload = {
  screenId: string;
  sectionId: string;
  elementId: string;
};

export type ReplaceFormElementPayload = Omit<AddFormElementPayload, "elementIndex">;

export type UpdateScreenDetailsPayload = {
  screenId: string;
  details: string;
  detailType: "name" | "description";
};

export type UpdateSectionTitlePayload = {
  screenId: string;
  sectionId: string;
  title: string;
};

export type SelectedFormElementPayload = {
  type: ElementType | "ScreenLayout" | "SectionLayout";
} & Omit<FormElement, "type">;

export type SelectedFormSectionPayload = {
  type: "SectionLayout";
} & FormSection;

export type AddFormSectionPayload = {
  screenId: string;
  sectionIndex: number;
  section: FormSection;
};

// export type ReplaceFormSectionPayload = {
//   screenId: string;
//   section: FormSection;
// };

export type RemoveFormSectionPayload = {
  screenId: string;
  sectionId: string;
};

export type ReplaceFormSectionPayload = Omit<AddFormSectionPayload, "sectionIndex">;

export type ReplaceScreenSectionsPayload = {
  screenId: string;
  sections: FormSection[];
};

export type ReplaceSectionElementsPayload = {
  screenId: string;
  sectionId: string;
  elements: FormElement[];
};
