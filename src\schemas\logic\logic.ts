import * as z from "zod";

const conditionSchema = z.object({
  id: z.string(),
  componentId: z.string().optional(),
  operator: z.string().optional(),
  value: z.string().or(z.number()).optional(),
  minimumValue: z.string().or(z.number()).optional(),
  maximumValue: z.string().or(z.number()).optional(),
  logic: z.string().nullable(),
});

const logicSchema = z.object({
  id: z.string(),
  conditions: z.array(conditionSchema).optional(),
  action: z.string().optional(),
});

export const elementLogicSchema = z.object({
  logics: z.array(logicSchema).min(1, "At least one logic is required"),
});
